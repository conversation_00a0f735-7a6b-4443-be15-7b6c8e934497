name = "temp-email"
main = "src/index.ts"
compatibility_date = "2024-03-01"
compatibility_flags = ["nodejs_compat"]

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "email-db"
database_id = "local-dev-db"

# 环境变量
[vars]
ENVIRONMENT = "development"
JWT_SECRET = "dev-jwt-secret-key-for-local-development-only"
BASE_DOMAIN = "localhost"
TURNSTILE_SECRET_KEY = "1x0000000000000000000000000000000AA"
TURNSTILE_SITE_KEY = "1x00000000000000000000AA"

# 生产环境配置
[env.production.vars]
ENVIRONMENT = "production"
JWT_SECRET = "your-jwt-secret-here"
BASE_DOMAIN = "yourapp.com"
TURNSTILE_SECRET_KEY = "your-turnstile-secret-key"
TURNSTILE_SITE_KEY = "your-turnstile-site-key"

# 开发环境配置
[env.development.vars]
ENVIRONMENT = "development"
JWT_SECRET = "dev-jwt-secret-key"
BASE_DOMAIN = "localhost"
TURNSTILE_SECRET_KEY = "dev-turnstile-secret"
TURNSTILE_SITE_KEY = "dev-turnstile-site"

# Email Routing 配置
[[email]]
name = "temp-email-routing"
destination_address = "<EMAIL>"

# 构建配置
[build]
command = "npm run build"
