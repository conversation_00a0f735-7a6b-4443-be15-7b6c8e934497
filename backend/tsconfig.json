{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/modules/*": ["./modules/*"], "@/handlers/*": ["./handlers/*"], "@/middleware/*": ["./middleware/*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"]}, "types": ["@cloudflare/workers-types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}